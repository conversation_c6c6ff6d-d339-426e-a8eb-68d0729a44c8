import React, { useCallback, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useBlocker, useNavigate, useParams } from "react-router-dom";

import {
  CalendarTodayOutlined,
  EmailOutlined,
  MessageOutlined,
  PhoneOutlined,
  PlaceOutlined,
  Tag,
} from "@mui/icons-material";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import PauseCircleOutlineRoundedIcon from "@mui/icons-material/PauseCircleOutlineRounded";
import {
  Avatar,
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid2 as Grid,
  Skeleton,
  Typography,
  styled,
} from "@mui/material";

import { useQuery } from "@tanstack/react-query";
import { AxiosResponse } from "axios";
// import { AxiosResponse } from "axios";
import { compact } from "lodash";

import Status from "@/common-components/status/status";

import BloodPressureSVG from "@/assets/image_svg/icons/blood_pressure.svg?react";
import ClockIconSVG from "@/assets/image_svg/icons/clockIcon.svg?react";
import PlaySVG from "@/assets/image_svg/icons/play_circle.svg?react";
import ReplaySVG from "@/assets/image_svg/icons/replay.svg?react";
import StopSVG from "@/assets/image_svg/icons/stop_circle.svg?react";
import { useDrawer } from "@/components/providers/DrawerProvider";
import ChipButton from "@/components/ui/Atom/ChipButton";
import IconButton from "@/components/ui/Atom/IconButton";
import LineProgress from "@/components/ui/Atom/LineProgress";
import useAuthority from "@/hooks/use-authority";
import { RootState } from "@/redux/store";
import { Patient, PatientControllerService, TimeLogControllerService } from "@/sdk/requests";
import { GetTenantId } from "@/services/common/get-tenant-id";
import { StoreActions } from "@/services/core/TimeLogService/StoreActions";
import TimeTrackingService from "@/services/core/TimeLogService/timeTrackingService";
import { AllowedTimeTrackingPages } from "@/services/core/TimeLogService/timeTrackingService";
import { DateNewFormat, formatTimeNewFormat } from "@/utils/format/date";
import { birthDate } from "@/utils/format/date";

import SendEmailForm from "./forms/SendEmailForm";
import SendMessageForm from "./forms/SendMessageForm";

interface ProfileInterFace {
  setPatientProfileData: (patient: Patient) => void;
}

interface ExtendedTimeLogRequest {
  totalTimeLogged: string;
}

// const capitalize = (str: string = "") => (str ? str[0].toUpperCase() : "");

const ProfileHeader = (props: ProfileInterFace) => {
  const { setPatientProfileData } = props;
  const { open: openDrawer, close: closeDrawer } = useDrawer();
  const { patientId } = useParams();
  const navigate = useNavigate();
  const { isProvider } = useAuthority();

  // Timer state
  const [elapsedTime, setElapsedTime] = useState(0);
  const [isTracking, setIsTracking] = useState(false);
  const [showNavigationDialog, setShowNavigationDialog] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState<string | null>(null);

  const [timeLogStats, setTimeLogStats] = useState({
    timeLogsData: [] as ExtendedTimeLogRequest[],
    totalSpentMinutes: 0,
    totalMinutes: 0,
  });

  // Get the current logged-in user profile data based on role
  const { data: currentUserData } = useSelector((state: RootState) =>
    isProvider ? state.providerProfileReducer : state.profileReducer
  );

  const {
    data: patientData,
    isLoading: loadingPatientData,
    isSuccess,
  } = useQuery({
    queryKey: ["patient", patientId],
    queryFn: async () => {
      const response = await PatientControllerService.getPatientById({
        patientUuid: String(patientId),
        xTenantId: GetTenantId(),
      });
      return response.data as Patient;
    },
    enabled: !!patientId,
  });

  const isCurrentUserPrimaryProvider = currentUserData?.uuid == Object.keys(patientData?.providerId || {})[0];
  const handleTimerUpdate = useCallback((seconds: number) => {
    setElapsedTime(seconds);
  }, []);

  const handleNavigationRestriction = useCallback(
    (options: { allowNavigation: boolean; message?: string; shouldShowDialog: boolean }) => {
      if (!options.allowNavigation && options.shouldShowDialog) {
        setShowNavigationDialog(true);
      }
    },
    []
  );

  useEffect(() => {
    if (isProvider) {
      TimeTrackingService.subscribe(handleTimerUpdate);
      TimeTrackingService.setNavigationRestrictionCallback(handleNavigationRestriction);

      setIsTracking(TimeTrackingService.getIsTracking());
      setElapsedTime(TimeTrackingService.getElapsedTime());

      return () => {
        TimeTrackingService.unsubscribe(handleTimerUpdate);
      };
    }
  }, [handleTimerUpdate, handleNavigationRestriction]);

  useEffect(() => {
    if (isProvider) {
      const updateTrackingState = () => {
        setIsTracking(StoreActions.getIsTracking());
      };

      const unsubscribe = StoreActions.subscribe(updateTrackingState);
      return unsubscribe;
    }
  }, []);

  useEffect(() => {
    if (isProvider) {
      if (!TimeTrackingService.getIsTracking()) {
        TimeTrackingService.startTracking();
      }
    }
  }, []);

  useEffect(() => {
    if (isSuccess) {
      setPatientProfileData(patientData);
    }
  }, [isSuccess, patientData, setPatientProfileData]);

  // Ensure time log is submitted on tab switch or browser close
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "hidden") {
        TimeTrackingService.stopTracking();
        TimeTrackingService.sendPayloadToBackend();
      }
    };
    const handleBeforeUnload = () => {
      TimeTrackingService.stopTracking();
      TimeTrackingService.sendPayloadToBackend();
    };
    document.addEventListener("visibilitychange", handleVisibilityChange);
    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, []);

  useEffect(() => {
    TimeTrackingService.sendPayloadToBackend();
  }, []);

  const handleStart = useCallback(() => {
    TimeTrackingService.startTracking();
  }, []);

  const handleStop = useCallback(() => {
    TimeTrackingService.stopTracking();
    setShowNavigationDialog(true);
  }, []);

  const handlePause = useCallback(() => {
    TimeTrackingService.pauseTracking();
  }, []);

  const handleReset = useCallback(() => {
    TimeTrackingService.reset();
    setElapsedTime(0);
  }, []);

  const formatTime = useCallback((totalSeconds: number) => {
    return TimeTrackingService.formatTime(totalSeconds);
  }, []);

  const handleConfirmdialog = async () => {
    // Stop tracking and update elapsed time
    TimeTrackingService.stopTracking();
    setElapsedTime(TimeTrackingService.getElapsedTime());
    await handleAccept();
    handleConfirmNavigation();
    setShowNavigationDialog(false);
    window.location.reload();
  };

  const handleAccept = async () => {
    await TimeTrackingService.sendPayloadToBackend();
  };

  const handleDrawer = {
    sendEmailForm: () => {
      openDrawer({
        title: "Compose Email",
        component: <SendEmailForm patientData={patientData as Patient} closeDrawer={closeDrawer} />,
      });
    },
    sendMessageForm: () => {
      openDrawer({
        title: "Compose Message",
        component: <SendMessageForm patientData={patientData as Patient} closeDrawer={closeDrawer} />,
      });
    },
  };

  const timeDisplay = formatTime(elapsedTime);
  const handleBeforeUnload = (event: BeforeUnloadEvent): void => {
    if (StoreActions.getIsTracking()) {
      event.preventDefault();
      event.returnValue = "";
    }
  };

  window.addEventListener("beforeunload", handleBeforeUnload);
  const [lastLocation, setLastLocation] = useState<string | null>(null);

  const isAllowedPage = (url: string): boolean => {
    const urlParams = new URLSearchParams(url.split("?")[1] || "");
    const tab = urlParams.get("tab");
    return tab ? AllowedTimeTrackingPages.includes(tab) : false;
  };

  const blocker = useBlocker((blocker) => {
    const nextLocation = blocker.nextLocation;
    return isTracking && nextLocation && !isAllowedPage(nextLocation.pathname + nextLocation.search);
  });

  useEffect(() => {
    if (blocker.state === "blocked") {
      const target = blocker.location.pathname + blocker.location.search;
      setLastLocation(target);
      setPendingNavigation(target);
      setShowNavigationDialog(true);
      TimeTrackingService.stopTracking();
      setElapsedTime(TimeTrackingService.getElapsedTime());
    }
  }, [blocker]);

  const handleConfirmNavigation = () => {
    setShowNavigationDialog(false);
    setPendingNavigation(null);

    if (lastLocation) {
      navigate(lastLocation);
    }
  };

  const handleCancelNavigation = () => {
    setShowNavigationDialog(false);
    setPendingNavigation(null);
    TimeTrackingService.flushData();
    if (pendingNavigation) {
      navigate(pendingNavigation);
    }
  };

  // Billing Threshold
  const { data: timeLogDataResponse, isSuccess: isSuccessTimeLogData } = useQuery({
    queryKey: [GetTenantId(), patientId],
    queryFn: () =>
      TimeLogControllerService.getAllPatientTimeLogs({
        xTenantId: GetTenantId(),
        patientId: patientId || "",
        page: 0,
        size: 10,
        month: `${new Date().toLocaleString("default", { month: "short" }).toUpperCase()}-${new Date().getFullYear()}`,
      }),
  });

  useEffect(() => {
    if (isSuccessTimeLogData) {
      const responseData = (timeLogDataResponse as unknown as AxiosResponse).data;
      const timeLogs = responseData?.totalTimeLogged || "";
      const totalMinutes = Math.ceil(Math.floor(Number(timeLogs) / 60) / 20) * 20;

      setTimeLogStats({
        timeLogsData: responseData?.content || [],
        totalSpentMinutes: Math.floor(Number(timeLogs) / 60),
        totalMinutes: totalMinutes === 0 ? 20 : totalMinutes,
      });
    }
  }, [timeLogDataResponse]);

  const getCurrentMonth = () => new Date().toLocaleString("default", { month: "long" });

  return (
    <>
      <Grid
        container
        spacing={2}
        sx={{
          justifyContent: "space-between",
          backgroundColor: "#F6F8FB",
          px: 2,
          py: 1.5,
          borderRadius: 2,
        }}
      >
        <Grid container size={{ xs: 12, md: 5 }} spacing={2} sx={{ alignItems: "flex-start" }}>
          <Grid size="auto">
            <Box sx={{ width: 76, aspectRatio: "1/1" }}>
              <Avatar src={patientData?.avatar} sx={{ width: "100%", height: "100%" }} />
            </Box>
          </Grid>
          <Grid container size="grow" direction="column" spacing={1.5}>
            <Grid container spacing={1} sx={{ justifyContent: "space-between" }}>
              <Grid>
                <Grid container spacing={1.5} sx={{ alignItems: "center" }}>
                  {loadingPatientData ? (
                    <Skeleton variant="text" width={100} height={22} />
                  ) : (
                    <Typography sx={{ fontSize: "1rem", fontWeight: "700", color: "#393939" }}>
                      {patientData?.firstName} {patientData?.lastName}
                    </Typography>
                  )}
                  <Status status={patientData?.active ? "ACTIVE" : "INACTIVE"} width="100px" />
                </Grid>
              </Grid>
              <Grid container spacing={1}>
                <ChipButton
                  icon={<EmailOutlined sx={{ fontSize: "16px" }} />}
                  label="Email"
                  onClick={handleDrawer.sendEmailForm}
                />
                <ChipButton
                  icon={<MessageOutlined sx={{ fontSize: "16px" }} />}
                  label="Message"
                  onClick={handleDrawer.sendMessageForm}
                />
              </Grid>
            </Grid>
            <Grid container rowSpacing={1} columnSpacing={4} flexWrap="wrap">
              <IconText icon={<Tag />} text={patientData?.mrn} isLoading={loadingPatientData} />
              <IconText
                icon={<CalendarTodayOutlined />}
                text={birthDate(patientData?.birthDate)}
                isLoading={loadingPatientData}
              />
              <IconText icon={<PhoneOutlined />} text={patientData?.mobileNumber} isLoading={loadingPatientData} />
              <IconText icon={<EmailOutlined />} text={patientData?.email} isLoading={loadingPatientData} />
              <IconText
                icon={<PlaceOutlined />}
                text={compact([
                  patientData?.address?.line1,
                  patientData?.address?.line2,
                  patientData?.address?.city,
                  patientData?.address?.state,
                  patientData?.address?.country,
                  patientData?.address?.zipcode,
                ]).join(", ")}
                isLoading={loadingPatientData}
              />
            </Grid>
          </Grid>
        </Grid>
        <Grid size={{ xs: 12, md: 7 }}>
          <Grid container spacing={1}>
            <Grid size="auto">
              <ContentBox>
                <Grid container direction="column" spacing="10px">
                  {!isCurrentUserPrimaryProvider && (
                    <Grid container direction="column" spacing="4px">
                      <Typography variant="small">Primary Provider</Typography>
                      <Typography variant="smallBold" fontWeight="500">
                        {loadingPatientData ? (
                          <Skeleton variant="text" />
                        ) : (
                          Object.values(patientData?.providerId || "")
                        )}
                      </Typography>
                    </Grid>
                  )}
                  <Grid container direction="column" spacing="4px">
                    <Typography variant="small">Assigned Nurse</Typography>
                    <Typography variant="smallBold" fontWeight="500">
                      {loadingPatientData ? <Skeleton variant="text" /> : Object.values(patientData?.nurseId || "")}
                    </Typography>
                  </Grid>
                </Grid>
              </ContentBox>
            </Grid>
            <Grid size="grow">
              <ProgressBox
                title="Monthly Data Compliance"
                icon={<BloodPressureSVG width={18} height={18} />}
                data={{ value: 6, total: 16 }}
                description="6 Days of devices data in February"
              />
            </Grid>
            <Grid size="grow">
              <ProgressBox
                title="Billing Threshold"
                icon={<ClockIconSVG width={18} height={18} />}
                data={{
                  value: timeLogStats.totalSpentMinutes,
                  total: timeLogStats.totalMinutes,
                }}
                description={`${timeLogStats.totalSpentMinutes} minutes reviewed in ${getCurrentMonth()}`}
              />
            </Grid>
            <Grid size="auto">
              {isProvider && (
                <ContentBox
                  sx={{ display: "flex", flexDirection: "column", gap: 1.5, justifyContent: "space-between" }}
                >
                  <Grid container justifyContent="space-between">
                    <Typography variant="medium">Log Time</Typography>
                    <Grid container spacing={1}>
                      <IconButton
                        icon={<StopSVG width={18} height={18} />}
                        onClick={handleStop}
                        disabled={!isTracking}
                      />
                      {isTracking ? (
                        <>
                          <IconButton
                            icon={<PauseCircleOutlineRoundedIcon width={18} height={18} fontSize="small" />}
                            onClick={handlePause}
                            disabled={!isTracking}
                          />
                        </>
                      ) : (
                        <IconButton
                          icon={<PlaySVG width={18} height={18} />}
                          onClick={handleStart}
                          disabled={isTracking}
                        />
                      )}
                      <IconButton icon={<ReplaySVG width={18} height={18} />} onClick={handleReset} />
                    </Grid>
                  </Grid>

                  <Grid
                    container
                    spacing="6px"
                    sx={{
                      backgroundColor: isTracking ? "#DCF7FF" : "#F5F5F5",
                      padding: "6px",
                      borderRadius: "4px",
                    }}
                  >
                    <Grid container spacing="1px" alignItems="flex-end">
                      <Typography variant="large" sx={{ color: isTracking ? "#006D8F" : "#666", lineHeight: 0.9 }}>
                        {timeDisplay.hours}
                      </Typography>
                      <Typography variant="small" sx={{ color: isTracking ? "#006D8F" : "#666" }}>
                        Hrs
                      </Typography>
                    </Grid>
                    <Grid container spacing="1px" alignItems="flex-end">
                      <Typography variant="large" sx={{ color: isTracking ? "#006D8F" : "#666", lineHeight: 0.9 }}>
                        {timeDisplay.minutes}
                      </Typography>
                      <Typography variant="small" sx={{ color: isTracking ? "#006D8F" : "#666" }}>
                        Mins
                      </Typography>
                    </Grid>
                    <Grid container spacing="1px" alignItems="flex-end">
                      <Typography variant="large" sx={{ color: isTracking ? "#006D8F" : "#666", lineHeight: 0.9 }}>
                        {timeDisplay.seconds}
                      </Typography>
                      <Typography variant="small" sx={{ color: isTracking ? "#006D8F" : "#666" }}>
                        Secs
                      </Typography>
                    </Grid>
                  </Grid>
                </ContentBox>
              )}
            </Grid>
          </Grid>
        </Grid>
      </Grid>

      {/* Navigation Restriction Dialog */}
      <Dialog
        open={showNavigationDialog}
        fullWidth
        // maxWidth="md"
        PaperProps={{
          sx: {
            borderRadius: "8px",
            boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.1)",
          },
        }}
      >
        <DialogTitle
          sx={{
            fontFamily: "Roboto",
            fontWeight: 600,
            fontSize: "24px",
            lineHeight: "100%",
            letterSpacing: "0%",
            marginTop: "16px",
            borderBottom: "1px solid #E8EBEC",
          }}
        >
          Confirm Log Time (Automatic)
        </DialogTitle>
        <DialogContent sx={{ padding: "24px" }}>
          <Box sx={{ display: "flex", alignItems: "flex-start", gap: 2, marginBottom: 3 }}>
            <InfoOutlinedIcon fontSize="small" sx={{ color: "#006D8F", marginTop: "20px" }} />
            <Typography
              sx={{
                fontFamily: "Roboto",
                fontWeight: 400,
                fontSize: 16,
                lineHeight: 1.5,
                color: "#666",
                marginTop: "20px",
              }}
            >
              By confirming, the time logged will be saved and reflected under the "RPM Time Log" tab.
            </Typography>
          </Box>

          <Box
            sx={{
              backgroundColor: "#F2F7F9",
              borderRadius: "4px",
              padding: "16px",
              marginTop: "16px",
            }}
          >
            <Typography
              variant="subtitle2"
              sx={{
                fontFamily: "Roboto",
                fontWeight: 500,
                fontSize: 16,
                lineHeight: "120%",
                letterSpacing: "0%",
                marginBottom: "8px",
                color: "#041D25",
              }}
            >
              Patient Monitoring
            </Typography>

            <Box
              sx={{ display: "grid", gridTemplateColumns: "repeat(3, 1fr)", gap: 1, fontSize: "14px", color: "#666" }}
            >
              <Box>
                <Typography variant="caption" sx={{ color: "#666", fontWeight: "600", display: "block" }}>
                  Date
                </Typography>
                <Typography
                  sx={{
                    fontFamily: "Roboto",
                    color: "#1C2427",
                    fontWeight: 500,
                    fontSize: 14,
                    lineHeight: "160%",
                    letterSpacing: "0%",
                  }}
                  variant="body1"
                >
                  {(() => {
                    const entry = StoreActions.getSingleEntry();
                    return entry ? DateNewFormat(entry.startTime) : DateNewFormat(new Date());
                  })()}
                </Typography>
              </Box>
              <Box>
                <Typography variant="caption" sx={{ color: "#666", fontWeight: "600", display: "block" }}>
                  Time
                </Typography>
                <Typography
                  sx={{
                    fontFamily: "Roboto",
                    color: "#1C2427",
                    fontWeight: 500,
                    fontSize: 14,
                    lineHeight: "160%",
                    letterSpacing: "0%",
                  }}
                  variant="body1"
                >
                  {(() => {
                    const entry = StoreActions.getSingleEntry();
                    return entry
                      ? `${formatTimeNewFormat(entry.startTime)} - ${formatTimeNewFormat(entry.endTime)}`
                      : "";
                  })()}
                </Typography>
              </Box>
              <Box>
                <Typography variant="caption" sx={{ color: "#666", fontWeight: "600", display: "block" }}>
                  Duration
                </Typography>
                <Typography
                  sx={{
                    fontFamily: "Roboto",
                    color: "#1C2427",
                    fontWeight: 500,
                    fontSize: 14,
                    lineHeight: "160%",
                    letterSpacing: "0%",
                  }}
                  variant="body1"
                >
                  {(() => {
                    const entry = StoreActions.getSingleEntry();
                    return StoreActions.formatDuration(entry?.duration || 0);
                  })()}
                </Typography>
              </Box>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions sx={{ padding: "16px 24px", borderTop: "1px solid #E8EBEC" }}>
          <Button onClick={handleCancelNavigation} color="error" variant="contained">
            Reject
          </Button>
          <Button
            onClick={async () => {
              handleConfirmdialog();
            }}
            variant="contained"
            sx={{
              backgroundColor: "#006D8F",
              textTransform: "none",
              "&:hover": {
                backgroundColor: "#006D8F",
              },
            }}
          >
            Accept
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

const IconText = ({ icon, text, isLoading }: { icon: React.ReactNode; text?: string; isLoading?: boolean }) => {
  return (
    <>
      {isLoading ? (
        <Skeleton variant="text" width={100} />
      ) : text ? (
        <Grid container size="auto" flexWrap="nowrap" spacing={1}>
          <Box sx={{ "& svg": { fontSize: 16 } }}>{icon}</Box>
          <Typography variant="iconText" sx={{ overflow: "auto", overflowWrap: "break-word" }}>
            {text}
          </Typography>
        </Grid>
      ) : null}
    </>
  );
};

const ProgressBox = ({
  title,
  data,
  description,
  icon,
}: {
  title: string;
  data: { value: number; total: number };
  description: string;
  icon?: React.ReactNode;
}) => {
  return (
    <ContentBox
      sx={{
        display: "flex",
        flexDirection: "column",
        justifyContent: "space-between",
        gap: 1.5,
      }}
    >
      <Grid container justifyContent="space-between" alignItems="flex-start" flexWrap="nowrap">
        <Typography variant="medium">{title}</Typography>
        <Box sx={{ flexShrink: 0 }}>{icon}</Box>
      </Grid>

      <Grid sx={{ textOverflow: "ellipsis", overflow: "hidden" }}>
        <Grid container alignItems="center" spacing={1}>
          <Typography variant="medium">{data.value}</Typography>
          <LineProgress variant="determinate" value={(data.value / data.total) * 100} sx={{ flexGrow: 1 }} />
          <Typography variant="medium">{data.total}</Typography>
        </Grid>
        <Typography variant="small" sx={{ whiteSpace: "nowrap" }}>
          {description}
        </Typography>
      </Grid>
    </ContentBox>
  );
};

const ContentBox = styled(Box)({
  border: "1px solid #E8EBEC",
  background: "#FFF",
  padding: "8px",
  borderRadius: "8px",
  height: "100%",
});

export default ProfileHeader;
